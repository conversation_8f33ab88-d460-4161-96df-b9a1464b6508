# Dynamic Query Common Issues and Fixes

This document covers common issues encountered with dynamic queries and their solutions.

## 1. Aggregate Function Empty Result Set Error

### Problem Description
Users encounter an EdgeDB error when executing aggregate queries on empty datasets:

```
gel.InvalidValueError: invalid input to mean(): not enough elements in input set
```

### Root Cause
EdgeDB's aggregate functions (`avg`, `min`, `max`, `sum`) fail when there are no rows to aggregate, as they cannot compute meaningful values from empty sets.

### Solution
Wrap aggregate functions with `coalesce()` to provide default values for empty result sets.

**Before (Failing):**
```json
{
  "Account": {
    "aggregate": {
      "averageMarketValue": "${avg(dailyBalances.endingMarketValue)}"
    }
  }
}
```

**After (Working):**
```json
{
  "Account": {
    "aggregate": {
      "averageMarketValue": "${coalesce(avg(dailyBalances.endingMarketValue), 0)}"
    }
  }
}
```

### Guidelines
- **Use coalesce with**: `avg()`, `min()`, `max()`, `sum()`
- **Don't need coalesce**: `count()` (returns 0 for empty sets)
- **Syntax**: `${coalesce(aggregate_function(field), default_value)}`
- **Common defaults**: `0` for numbers, `''` for strings, `null` for optional values

## 2. Date Type Mismatch Error

### Problem Description
Users encounter EdgeDB function signature mismatch when comparing dates:

```
function "differenceInMonths(arg0: std::datetime, arg1: cal::local_date)" does not exist
```

### Root Cause
Type mismatch between `now()` (returns `std::datetime`) and date fields like `periodEndDate` (stored as `cal::local_date`).

### Solution
Use `today()` instead of `now()` for date-only comparisons.

**Before (Failing):**
```json
{
  "Account": {
    "select": ["id", "accountNumber", "accountName", "balance"],
    "filter": "differenceInMonths(now(), dailyBalances.periodEndDate) > 6"
  }
}
```

**After (Working):**
```json
{
  "Account": {
    "select": ["id", "accountNumber", "accountName", "balance"],
    "filter": "differenceInMonths(today(), dailyBalances.periodEndDate) > 6"
  }
}
```

### Function Reference
| Function | Return Type | EdgeDB Type | Use Case |
|----------|-------------|-------------|----------|
| `now()` | Date | `std::datetime` | Datetime comparisons |
| `today()` | Date | `cal::local_date` | Date-only comparisons |
| `currentDate()` | string | ISO date string | String operations |

## Implementation Changes

### 1. Enhanced Error Handling
Updated `app/services/new_query_agent/dynamic_query.py` to provide clearer error messages for empty result sets.

### 2. Updated Prompt Guidelines
Added guidance in `app/services/new_query_agent/prompts/dynamic_query_prompt.txt`:

```
- IMPORTANT: For aggregate functions that may return null on empty datasets (avg, min, max, sum), 
  wrap them with coalesce to provide default values. Example: "${coalesce(avg(balance), 0)}"
  
- IMPORTANT: For date comparisons with date fields (like periodEndDate, periodStartDate), 
  use today() instead of now() to ensure type compatibility.
```

### 3. Enhanced Documentation
Added practical examples to function definitions showing proper usage patterns.

## Best Practices

### For Developers
1. **Always use coalesce** with aggregate functions that can return null
2. **Choose correct date functions** based on field types in schema
3. **Test queries** with empty datasets to ensure robustness

### For AI Query Generation
The updated prompts now include specific guidance to prevent these issues in future AI-generated queries.

## Related Files Modified
- `app/services/new_query_agent/dynamic_query.py` - Enhanced error handling
- `app/services/new_query_agent/prompts/dynamic_query_prompt.txt` - Added guidelines and examples
- `docs/dynamic_query_fixes.md` - This documentation
