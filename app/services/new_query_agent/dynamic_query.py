from ast import Dict
import json
import os
import re
import sys
import traceback
import asyncio
from pathlib import Path
from typing import Union

sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from app.core.config import AUTH_PROPERTIES, SERVICES_PROPERTIES, SETTINGS
from app.services.new_query_agent.utils.dynamic_query_validator import QueryValidator


from google import genai
from google.genai import types as genai_types

BASE_PATH = os.path.dirname(os.path.abspath(__file__))

from app.core.auth import get_headers
from httpx import AsyncClient, Timeout


async def call_dynamic_query(payload: Dict):
    base_url = SERVICES_PROPERTIES.BASE_URL
    bo_name = next(iter(payload.keys()))
    url = base_url + f"/api/domain/wealthdomain/{bo_name}/dynamic/query"
    headers = await get_headers()

    if "select" in payload[bo_name] and "limit" not in payload[bo_name]:
        payload[bo_name]["limit"] = 10

    async with <PERSON>ync<PERSON><PERSON>(timeout=Timeout(timeout=10)) as client:
        response = await client.post(url, headers=headers, json=payload)
        if not response.is_success:
            
        response.raise_for_status()
        return response.json()


def _extract_json_from_llm_output(llm_output: str) -> dict:
    """Extract and validate JSON from LLM output."""
    # print(f"Raw LLM output: {llm_output}")

    try:
        # Convert AIMessage to string if needed
        if hasattr(llm_output, "content"):
            llm_output = llm_output.content

        # Try to find JSON content between triple backticks if present
        if "```json" in llm_output:
            start = llm_output.find("```json") + 7
            end = llm_output.find("```", start)
            if end != -1:
                llm_output = llm_output[start:end]
        elif "```" in llm_output:
            start = llm_output.find("```") + 3
            end = llm_output.find("```", start)
            if end != -1:
                llm_output = llm_output[start:end]

        # Clean and format the JSON string
        llm_output = llm_output.strip()

        # Only handle unquoted keys
        llm_output = re.sub(
            r"([{,])\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:", r'\1"\2":', llm_output
        )
        return json.loads(llm_output)
    except json.JSONDecodeError as e:
        print(f"JSON decode error: {e}")
        print(f"Failed JSON string: {llm_output}")
        return {}
    except Exception as e:
        print(f"Unexpected error: {e}")
        return {}


class DynamicQueryGenerator:
    def __init__(self, previous_messages: Union[Dict, list[Dict]] = []) -> None:
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )

        prompt_file_path = os.path.join(BASE_PATH, "./prompts/dynamic_query_prompt.txt")

        with open(prompt_file_path, "r") as fp:
            prompt = fp.read()

        self.schema_file_path = os.path.join(BASE_PATH, "./data/schema.json")

        # prompt_file_path = os.path.join(
        #     BASE_PATH, "./prompts/new_dynamic_query_prompt.md"
        # )

        # with open(prompt_file_path, "r") as fp:
        #     prompt = fp.read()

        # self.schema_file_path = os.path.join(BASE_PATH, "./data/schema.json")
        # with open(self.schema_file_path, "r") as fp:
        #     schema = json.load(fp)

        # functions_file_path = os.path.join(BASE_PATH, "./data/functions.json")
        # with open(functions_file_path, "r") as fp:
        #     functions = json.load(fp)

        # examples_file_path = os.path.join(BASE_PATH, "./data/examples.json")
        # with open(examples_file_path, "r") as fp:
        #     examples = json.load(fp)
        
        # instructions_file_path = os.path.join(BASE_PATH, "./prompts/instructions.txt")
        # with open(instructions_file_path, "r") as fp:
        #     instructions = fp.read()

        # prompt = prompt.replace("{{ schema }}", json.dumps(schema, indent=2))
        # prompt = prompt.replace("{{ functions }}", json.dumps(functions, indent=2))
        # prompt = prompt.replace("{{ examples }}", json.dumps(examples, indent=2))
        # prompt = prompt.replace("{{ instructions }}", instructions)

        self.generation_config = genai_types.GenerateContentConfig(
            temperature=0,
            system_instruction=prompt
        )
        self.messages = []
        if previous_messages:
            self.messages.append(
                genai_types.Content(
                    role="user",
                    parts=[
                        {
                            "text": f"Previous messages between user and the model is given below \n {previous_messages}"
                        }
                    ],
                )
            )

    async def generate(self, user_query, max_tries=2):

        messages = self.messages
        messages.append(
            genai_types.Content(
                role="user",
                parts=[{"text": user_query}],
            )
        )
        try:
            for _ in range(max_tries):
                response = await self.genai_client.aio.models.generate_content(
                    model="gemini-2.0-flash",
                    contents=messages,
                    config=self.generation_config,
                )

                query_payload = _extract_json_from_llm_output(response.text)
                if len(query_payload) == 0:
                    print("No query generated")
                    return None

                validator = QueryValidator(self.schema_file_path)
                is_valid, errors = validator.validate(query_payload)
                if is_valid:
                    return query_payload
                else:
                    print(f"Validation errors found.\n {errors} \n Retrying...")

                    messages.extend(
                        [
                            genai_types.Content(
                                role="model",
                                parts=[{"text": response.text}],
                            ),
                            genai_types.Content(
                                role="user",
                                parts=[
                                    {
                                        "text": f"The query is not valid. Please correct the query. \n Errors \n {errors}"
                                    }
                                ],
                            ),
                        ]
                    )
        except Exception as e:
            print("Error while generating dynamic query:", e)
            traceback.print_exc()

        return None


async def execute_query(query):
    response = await DynamicQueryGenerator(previous_messages).generate(query)
    print("Generated query:", json.dumps(response, indent=2))
    if response:
        result = await call_dynamic_query(response)
        print("Query result:", json.dumps(result, indent=2))


if __name__ == "__main__":
    query = "Which accounts have had no activity in the past 6 months?"
    previous_messages = []
    response = asyncio.run(execute_query(query))

    # questions = [
    #     "Get all accounts with balance over 1000 owned by Alice, sorted by account number",
    #     "How many accounts are associated with rep code 8257985",
    #     "What is the total balance of all accounts associated with rep code 8257985",
    #     "What is the total balance of all accounts associated with rep code 8257985 and owned by Alice",
    # ]

    # for question in questions:
    #     response = asyncio.run(DynamicQueryGenerator().generate(question))
    #     print(f"Question: {question}")
    #     print("Generated query:", json.dumps(response, indent=2))
