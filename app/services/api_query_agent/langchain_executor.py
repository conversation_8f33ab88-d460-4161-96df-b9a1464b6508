from langchain.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.caches import BaseCache
from langchain_core.callbacks import Callbacks
from langchain_openai import ChatOpenAI

# Rebuild the ChatOpenAI model to resolve Pydantic issues
ChatOpenAI.model_rebuild()

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from app.core.context import global_context


api_key = ""

# For enabling lanchain debuggin
# from langchain.globals import set_debug
# set_debug(True)

MODEL_NAME = "o4-mini-2025-04-16"


def get_model():
    config_details = {
        "model_name": MODEL_NAME,
        "temperature": 1,
        "max_retries": 3,
    }
    model = ChatOpenAI(**config_details, api_key=api_key)
    return model


def get_chain():
    model = get_model()
    parser = JsonOutputParser()

    prompt = PromptTemplate(
        template="{query}",
        input_variables=["query"],
    )

    data_generator_chain = prompt | model | parser
    return model, data_generator_chain


def run_query(prompt=""):
    modl, chain = get_chain()
    res = chain.invoke({"query": prompt})
    return res


if __name__ == "__main__":
    PROMPT = "hello. give response in json"
    res = run_query(PROMPT)
    print(res)
